#!/usr/bin/env python3
"""
Simple test to debug the processing issue
"""

import sys
import os
import numpy as np
import time

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from face_tracking_demo import AdvancedFaceTrackingProcessor
from showcase_framework import FramePacket

def test_processing():
    """Test the processing with a mock DetectionResults object."""
    print("Creating processor...")
    
    processor = AdvancedFaceTrackingProcessor(
        conf_threshold=0.5,
        tracking_sensitivity='medium',
        enable_tracking=False  # Disable tracking for simple test
    )
    
    print("Creating test frame...")
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Create a mock DetectionResults object that mimics the real one
    class MockDetectionResults:
        def __init__(self):
            self.results = [
                {'bbox': [100, 100, 200, 200], 'score': 0.9, 'label': 'face'},
                {'bbox': [300, 300, 400, 400], 'score': 0.8, 'label': 'face'}
            ]
    
    mock_results = MockDetectionResults()
    
    print("Creating frame packet...")
    pkt = FramePacket(
        frame_idx=1,
        frame_rgb=frame,
        model_out=mock_results,
        timestamp=time.time(),
        meta={}
    )
    
    print("Testing processing...")
    try:
        result = processor.process(pkt)
        print(f"✓ Processing successful! Result shape: {result.shape}")
        return True
    except Exception as e:
        print(f"✗ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_processing():
    """Test the processing with empty results."""
    print("\nTesting empty results...")
    
    processor = AdvancedFaceTrackingProcessor(
        conf_threshold=0.5,
        tracking_sensitivity='medium',
        enable_tracking=False
    )
    
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Create empty results
    class EmptyDetectionResults:
        def __init__(self):
            self.results = []
    
    empty_results = EmptyDetectionResults()
    
    pkt = FramePacket(
        frame_idx=1,
        frame_rgb=frame,
        model_out=empty_results,
        timestamp=time.time(),
        meta={}
    )
    
    try:
        result = processor.process(pkt)
        print(f"✓ Empty processing successful! Result shape: {result.shape}")
        return True
    except Exception as e:
        print(f"✗ Empty processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_none_processing():
    """Test the processing with None results."""
    print("\nTesting None results...")
    
    processor = AdvancedFaceTrackingProcessor(
        conf_threshold=0.5,
        tracking_sensitivity='medium',
        enable_tracking=False
    )
    
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    pkt = FramePacket(
        frame_idx=1,
        frame_rgb=frame,
        model_out=None,
        timestamp=time.time(),
        meta={}
    )
    
    try:
        result = processor.process(pkt)
        print(f"✓ None processing successful! Result shape: {result.shape}")
        return True
    except Exception as e:
        print(f"✗ None processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("="*60)
    print("FACE TRACKING PROCESSING TESTS")
    print("="*60)
    
    tests = [
        ("Mock DetectionResults", test_processing),
        ("Empty Results", test_empty_processing),
        ("None Results", test_none_processing)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("TEST RESULTS:")
    print("="*60)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("="*60)
    
    if all_passed:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed.")
