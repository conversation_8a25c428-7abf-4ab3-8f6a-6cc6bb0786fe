#!/usr/bin/env python3
"""
Test script to verify EOF handling fix in VideoFileSource.
"""

import time
import logging
from showcase_framework import VideoFileSource

# Configure logging
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")
logger = logging.getLogger(__name__)

def test_eof_handling():
    """Test that VideoFileSource properly signals EOF and exits."""
    print("Testing EOF handling...")
    
    video_path = "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4"
    
    # Test with buffering (the problematic case)
    source = VideoFileSource(
        path=video_path,
        realtime=True,
        speed=10.0,  # 10x speed for faster testing
        buffer_size=5,
        loop=False  # Important: no looping
    )
    
    source.start()
    start_time = time.time()
    frame_count = 0
    
    print("Reading frames until EOF...")
    
    while True:
        frame = source.read()
        if frame is None:
            print(f"EOF reached after {frame_count} frames")
            break
            
        frame_count += 1
        
        # Print progress every 50 frames
        if frame_count % 50 == 0:
            stats = source.get_stats()
            print(f"Frame {frame_count}: progress={stats['progress']:.1%}")
    
    elapsed = time.time() - start_time
    stats = source.get_stats()
    
    print(f"Test completed in {elapsed:.2f}s")
    print(f"Total frames read: {frame_count}")
    print(f"Expected frames: {stats.get('frame_count', 'unknown')}")
    print(f"Effective FPS: {stats['effective_fps']:.1f}")
    
    source.stop()
    print("✓ VideoFileSource properly handled EOF and stopped")

def test_direct_read_eof():
    """Test EOF handling with direct read (no buffering)."""
    print("\nTesting direct read EOF handling...")
    
    video_path = "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4"
    
    # Test without buffering
    source = VideoFileSource(
        path=video_path,
        realtime=False,
        buffer_size=1,  # Minimal buffer to force direct reads
        loop=False
    )
    
    source.start()
    frame_count = 0
    
    while True:
        frame = source.read()
        if frame is None:
            print(f"Direct read EOF reached after {frame_count} frames")
            break
        frame_count += 1
        
        # Only read first 10 frames for quick test
        if frame_count >= 10:
            break
    
    source.stop()
    print("✓ Direct read EOF handling works")

if __name__ == "__main__":
    test_eof_handling()
    test_direct_read_eof()
    print("\n✅ All EOF tests passed!")
