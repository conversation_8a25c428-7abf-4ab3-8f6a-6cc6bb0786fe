# Face Detection and Tracking Demonstration

## Overview

The `face_tracking_demo.py` is a comprehensive demonstration program that showcases the enhanced supervision integration capabilities of the `showcase_framework.py`. It provides professional-grade face detection and tracking with real-time visualization, performance monitoring, and advanced features.

## Features

### 🎯 **Core Capabilities**
- **Real-time Face Detection**: Uses Hailo RetinaFace model for high-performance face detection
- **Object Tracking**: Advanced ByteTrack integration with unique ID assignment and persistence
- **Professional Visualization**: Supervision library integration for high-quality annotations
- **Performance Monitoring**: Real-time FPS, detection statistics, and tracking metrics
- **Multiple Input Sources**: Support for video files, webcam, and Raspberry Pi camera

### 🚀 **Advanced Features**
- **Tracking Trails**: Visual trails showing face movement history
- **Configurable Sensitivity**: Low, medium, and high tracking sensitivity levels
- **Statistics Overlay**: Real-time performance metrics displayed on video
- **Headless Mode**: Run without GUI for server/embedded deployments
- **Video Recording**: Save processed output with tracking annotations
- **Graceful Degradation**: Automatic fallback when components are unavailable

### 🎨 **Visualization Features**
- **Unique Tracking IDs**: Each face gets a persistent ID (e.g., "Face #1", "Face #2")
- **Confidence Scores**: Real-time confidence display for each detection
- **Tracking Trails**: Optional movement history visualization
- **Professional Annotations**: High-quality bounding boxes and labels
- **Performance Overlay**: FPS, detection count, and tracking statistics

## Installation and Setup

### Prerequisites
```bash
# Ensure you have the enhanced showcase_framework.py
# Install required dependencies
pip install supervision opencv-python numpy degirum

# For Raspberry Pi camera support
pip install picamera2
```

### Model Setup
```bash
# Ensure DeGirum model zoo is available
export DEGIRUM_ZOO_PATH="~/degirum-zoo"

# Required model: retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1
```

## Usage Examples

### 1. Basic Video File Processing
```bash
# Process video file with medium tracking sensitivity
python face_tracking_demo.py --video-file video.mp4 --conf 0.5 --tracking-sensitivity medium

# With tracking trails and statistics
python face_tracking_demo.py --video-file video.mp4 --conf 0.4 --show-trails --log-performance
```

### 2. Webcam Real-time Processing
```bash
# Real-time webcam processing with high sensitivity
python face_tracking_demo.py --webcam --conf 0.3 --tracking-sensitivity high --show-trails

# Headless webcam processing (no display)
python face_tracking_demo.py --webcam --headless --log-performance
```

### 3. Raspberry Pi Camera
```bash
# Use Raspberry Pi camera with custom settings
python face_tracking_demo.py --conf 0.5 --tracking-sensitivity medium --show-trails
```

### 4. Output Video Recording
```bash
# Save processed video with tracking annotations
python face_tracking_demo.py --video-file input.mp4 --output tracked_output.mp4 --conf 0.4 --show-trails

# Custom FPS and quality settings
python face_tracking_demo.py --video-file input.mp4 --output output.mp4 --fps 25 --conf 0.5
```

### 5. Performance Testing and Debugging
```bash
# Headless mode with detailed performance logging
python face_tracking_demo.py --video-file video.mp4 --headless --log-performance --debug

# Process limited frames for testing
python face_tracking_demo.py --video-file video.mp4 --max-frames 100 --headless
```

## Command-Line Options

### Input Sources
- `--video-file PATH`: Use video file as input
- `--webcam`: Use webcam (camera index 0)
- Default: Raspberry Pi camera (Picamera2)

### Detection and Tracking
- `--conf FLOAT`: Confidence threshold (default: 0.5)
- `--tracking-sensitivity {low,medium,high}`: Tracking sensitivity (default: medium)
- `--no-tracking`: Disable tracking (detection only)

### Visualization
- `--show-trails`: Enable tracking trails/history
- `--trail-length INT`: Trail length in frames (default: 30)
- `--no-stats`: Hide performance statistics overlay
- `--headless`: Run without display window

### Output and Recording
- `--output PATH`: Save output video file
- `--fps FLOAT`: Output video FPS (default: 30.0)

### Performance and Debugging
- `--log-performance`: Log detailed performance statistics
- `--debug`: Enable debug logging
- `--max-frames INT`: Limit number of frames processed

## Architecture and Components

### Core Classes

#### 1. **AdvancedFaceTrackingProcessor**
- Extends `EnhancedFrameProcessor` from showcase_framework
- Integrates face detection, tracking, and visualization
- Configurable tracking sensitivity and visualization options

#### 2. **FaceTrackingStats**
- Thread-safe statistics collection
- Real-time FPS calculation and performance metrics
- Tracking duration and face count statistics

#### 3. **TrackingTrailsVisualizer**
- Visual tracking trails with fading effects
- Unique colors for different tracked faces
- Configurable trail length and appearance

### Integration with Showcase Framework

The demo leverages the enhanced supervision integration:

```python
# Uses EnhancedFrameProcessor with supervision integration
processor = AdvancedFaceTrackingProcessor(
    conf_threshold=0.5,
    tracking_sensitivity='medium',
    enable_tracking=True,
    show_trails=True
)

# Automatic conversion to supervision format
sv_detections = processor.get_supervision_detections(frame_packet)

# Professional visualization with tracking IDs
annotated_frame = processor.process(frame_packet)
```

## Performance Characteristics

### Tracking Sensitivity Levels

#### Low Sensitivity
- **Activation Threshold**: 0.7
- **Lost Track Buffer**: 15 frames
- **Matching Threshold**: 0.9
- **Best For**: Stable, high-confidence detections

#### Medium Sensitivity (Default)
- **Activation Threshold**: 0.5
- **Lost Track Buffer**: 30 frames
- **Matching Threshold**: 0.8
- **Best For**: Balanced performance and accuracy

#### High Sensitivity
- **Activation Threshold**: 0.3
- **Lost Track Buffer**: 50 frames
- **Matching Threshold**: 0.7
- **Best For**: Detecting and tracking difficult faces

### Performance Metrics

The demo provides comprehensive performance monitoring:

- **Real-time FPS**: Current and average frame rates
- **Detection Statistics**: Total faces detected, detection rate
- **Tracking Metrics**: Active tracks, unique faces seen
- **System Performance**: Processing time, queue status

## Interactive Controls

When running in GUI mode:

- **'q'**: Quit the application
- **'r'**: Reset statistics counters
- **'s'**: Save screenshot of current frame

## Error Handling and Fallbacks

The demo includes comprehensive error handling:

1. **Model Fallback**: Falls back to alternative models or dummy model if RetinaFace unavailable
2. **Source Fallback**: Tries webcam → Picamera2 → error if no source available
3. **Supervision Graceful Degradation**: Works without supervision library (basic mode)
4. **Threading Safety**: All components are thread-safe for pipeline operation

## Output and Statistics

### Final Statistics Report
```
============================================================
FINAL STATISTICS
============================================================
Total Runtime: 45.2 seconds
Frames Processed: 1356
Average FPS: 30.0
Frames with Faces: 892 (65.8%)
Total Detections: 1247
Unique Faces Tracked: 5
Max Concurrent Faces: 3
Longest Track Duration: 234 frames
============================================================
```

### Real-time Overlay Information
- Current and average FPS
- Number of active faces and tracks
- Total frames processed and detection rate
- Unique faces seen and maximum concurrent faces
- Runtime and performance metrics

## Integration Examples

### Custom Processing Pipeline
```python
from face_tracking_demo import AdvancedFaceTrackingProcessor
from showcase_framework import VideoFileSource, DegirumRetinaFaceModel, BufferedPipeline

# Create components
source = VideoFileSource("input.mp4")
model = DegirumRetinaFaceModel("retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1")
processor = AdvancedFaceTrackingProcessor(
    conf_threshold=0.5,
    tracking_sensitivity='high',
    show_trails=True
)

# Create and run pipeline
pipeline = BufferedPipeline(source, model, processor)
pipeline.start()
```

### Statistics Monitoring
```python
# Get real-time statistics
stats = processor.get_statistics()
print(f"FPS: {stats['current_fps']:.1f}")
print(f"Active Tracks: {stats['active_tracks']}")
print(f"Unique Faces: {stats['unique_faces_seen']}")
```

## Troubleshooting

### Common Issues

1. **"Supervision library not available"**
   - Install: `pip install supervision`

2. **"Failed to load RetinaFace model"**
   - Check DeGirum zoo path: `--zoo-path ~/degirum-zoo`
   - Verify model availability in zoo

3. **"No video source available"**
   - For webcam: Check camera permissions and availability
   - For Picamera2: Install `pip install picamera2`
   - Use `--video-file` for file input

4. **Poor tracking performance**
   - Adjust `--tracking-sensitivity` (try 'high')
   - Lower `--conf` threshold (try 0.3-0.4)
   - Check lighting and video quality

### Performance Optimization

- Use `--headless` for better performance
- Adjust confidence threshold based on use case
- Consider tracking sensitivity vs. accuracy trade-offs
- Monitor performance with `--log-performance`

## Future Enhancements

Potential improvements and extensions:

1. **Face Recognition Integration**: Add face identification capabilities
2. **Multi-camera Support**: Process multiple camera streams
3. **Cloud Integration**: Stream to cloud services
4. **Advanced Analytics**: Age/gender estimation, emotion detection
5. **Mobile App Integration**: Remote monitoring and control

---

This demonstration showcases the power and flexibility of the enhanced showcase framework with supervision integration, providing a professional-grade face tracking solution suitable for various applications from security systems to interactive installations.
