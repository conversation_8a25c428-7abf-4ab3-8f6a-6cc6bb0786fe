#!/usr/bin/env python3
"""
Comprehensive test script for the enhanced supervision integration in showcase_framework.py

This script validates:
1. Supervision conversion functionality
2. Tracking integration
3. Performance and caching
4. Error handling and edge cases
5. Backward compatibility
"""

import sys
import os
import numpy as np
import time
from typing import List, Dict, Any

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the enhanced framework
from showcase_framework import (
    SupervisionConverter, SupervisionTracker, EnhancedFrameProcessor,
    DetectionSourceFactory, convert_detections_to_supervision,
    create_hailo_showcase_processor, Detection, FramePacket,
    SUPERVISION_AVAILABLE
)

def test_supervision_availability():
    """Test if supervision library is available."""
    print("=== Testing Supervision Availability ===")
    
    if SUPERVISION_AVAILABLE:
        print("✓ Supervision library is available")
        import supervision as sv
        print(f"✓ Supervision version: {getattr(sv, '__version__', 'unknown')}")
        return True
    else:
        print("✗ Supervision library not available")
        print("  Install with: pip install supervision")
        return False

def test_supervision_converter():
    """Test the SupervisionConverter class."""
    print("\n=== Testing SupervisionConverter ===")
    
    converter = SupervisionConverter(enable_caching=True)
    
    # Test Hailo format conversion
    hailo_detections = [
        {"bbox": [100, 100, 200, 200], "score": 0.95, "label": "person"},
        {"bbox": [300, 150, 400, 250], "score": 0.87, "label": "face"},
        {"bbox": [50, 50, 150, 150], "score": 0.75, "emb": np.random.rand(512)}
    ]
    
    print(f"Input: {len(hailo_detections)} Hailo detections")
    
    # Convert to supervision format
    sv_detections = converter.convert_to_supervision(
        detections=hailo_detections,
        source_format='hailo',
        frame_shape=(480, 640),
        class_names=['person', 'face', 'person']
    )
    
    if sv_detections is None:
        print("✗ Conversion to supervision format failed")
        return False
    
    print(f"✓ Converted to supervision format: {len(sv_detections)} detections")
    print(f"  xyxy shape: {sv_detections.xyxy.shape}")
    print(f"  confidence shape: {sv_detections.confidence.shape}")
    print(f"  class_id shape: {sv_detections.class_id.shape}")
    print(f"  data keys: {list(sv_detections.data.keys())}")
    
    # Test conversion back to Hailo format
    converted_back = converter.convert_from_supervision(sv_detections, target_format='hailo')
    print(f"✓ Converted back to Hailo format: {len(converted_back)} detections")
    
    # Test caching
    stats = converter.get_cache_stats()
    print(f"✓ Cache stats: hits={stats['hits']}, misses={stats['misses']}, hit_rate={stats['hit_rate']:.2f}")
    
    # Test empty detections
    empty_sv = converter.convert_to_supervision([])
    if empty_sv is not None and len(empty_sv) == 0:
        print("✓ Empty detections handled correctly")
    else:
        print("✗ Empty detections not handled correctly")
        return False
    
    return True

def test_supervision_tracker():
    """Test the SupervisionTracker class."""
    print("\n=== Testing SupervisionTracker ===")
    
    if not SUPERVISION_AVAILABLE:
        print("⚠ Skipping tracker tests - supervision not available")
        return True
    
    tracker = SupervisionTracker(
        tracker_type='bytetrack',
        track_activation_threshold=0.25,
        lost_track_buffer=30,
        minimum_matching_threshold=0.8
    )
    
    if not tracker.is_available():
        print("✗ Tracker initialization failed")
        return False
    
    print("✓ Tracker initialized successfully")
    
    # Create sample detections
    converter = SupervisionConverter()
    hailo_detections = [
        {"bbox": [100, 100, 200, 200], "score": 0.95},
        {"bbox": [300, 150, 400, 250], "score": 0.87}
    ]
    
    sv_detections = converter.convert_to_supervision(
        hailo_detections, 
        source_format='hailo',
        frame_shape=(480, 640)
    )
    
    # Update tracker
    tracked_detections = tracker.update_with_detections(sv_detections)
    
    if tracked_detections is not None:
        print(f"✓ Tracking update successful: {len(tracked_detections)} detections")
        if hasattr(tracked_detections, 'tracker_id') and tracked_detections.tracker_id is not None:
            print(f"  Tracker IDs: {tracked_detections.tracker_id}")
            print("✓ Tracker IDs assigned successfully")
        else:
            print("⚠ Tracker IDs not assigned (may be normal for first frame)")
    else:
        print("✗ Tracking update failed")
        return False
    
    return True

def test_enhanced_frame_processor():
    """Test the EnhancedFrameProcessor class."""
    print("\n=== Testing EnhancedFrameProcessor ===")
    
    processor = EnhancedFrameProcessor(
        conf_threshold=0.3,
        enable_supervision=True,
        enable_tracking=True,
        tracker_config={'tracker_type': 'bytetrack'}
    )
    
    print(f"✓ EnhancedFrameProcessor initialized (supervision={processor.enable_supervision}, tracking={processor.enable_tracking})")
    
    # Create test frame packet
    frame_rgb = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    model_out = [
        {"bbox": [100, 100, 200, 200], "score": 0.95, "label": "person"},
        {"bbox": [300, 150, 400, 250], "score": 0.87, "label": "face"}
    ]
    
    frame_packet = FramePacket(
        frame_rgb=frame_rgb,
        model_out=model_out,
        timestamp=time.time(),
        frame_idx=0,
        meta={}
    )
    
    # Test supervision processing
    if processor.enable_supervision:
        processed_frame = processor.process_with_supervision(
            frame_packet,
            source_format='hailo',
            class_names=['person', 'face']
        )
        
        if processed_frame is not None and processed_frame.shape == frame_rgb.shape:
            print("✓ Supervision processing successful")
        else:
            print("✗ Supervision processing failed")
            return False
        
        # Test getting supervision detections
        sv_detections = processor.get_supervision_detections(
            frame_packet,
            source_format='hailo'
        )
        
        if sv_detections is not None:
            print(f"✓ Got supervision detections: {len(sv_detections)} detections")
        else:
            print("✗ Failed to get supervision detections")
            return False
    else:
        print("⚠ Supervision processing skipped - not available")
    
    return True

def test_factory_functions():
    """Test factory functions and convenience methods."""
    print("\n=== Testing Factory Functions ===")
    
    # Test DetectionSourceFactory
    processor = DetectionSourceFactory.create_enhanced_processor(
        conf_threshold=0.5,
        enable_supervision=True,
        enable_tracking=True
    )
    
    print("✓ DetectionSourceFactory.create_enhanced_processor successful")
    
    # Test create_hailo_showcase_processor
    hailo_processor = create_hailo_showcase_processor(
        conf_threshold=0.4,
        enable_tracking=True,
        tracking_sensitivity='high'
    )
    
    print("✓ create_hailo_showcase_processor successful")
    
    # Test convert_detections_to_supervision convenience function
    hailo_dets = [{"bbox": [10, 10, 50, 50], "score": 0.9}]
    sv_dets = convert_detections_to_supervision(
        hailo_dets,
        source_format='hailo',
        frame_shape=(100, 100)
    )
    
    if sv_dets is not None and len(sv_dets) == 1:
        print("✓ convert_detections_to_supervision successful")
    else:
        print("✗ convert_detections_to_supervision failed")
        return False
    
    return True

def test_performance_and_caching():
    """Test performance and caching functionality."""
    print("\n=== Testing Performance and Caching ===")
    
    converter = SupervisionConverter(enable_caching=True, cache_size=10)
    
    # Create test data
    test_detections = [
        {"bbox": [i*10, i*10, i*10+50, i*10+50], "score": 0.9}
        for i in range(5)
    ]
    
    # Time conversions
    start_time = time.time()
    for _ in range(10):
        sv_dets = converter.convert_to_supervision(
            test_detections,
            source_format='hailo',
            frame_shape=(480, 640)
        )
    
    elapsed = time.time() - start_time
    stats = converter.get_cache_stats()
    
    print(f"✓ Performance test completed in {elapsed:.3f}s")
    print(f"  Cache hit rate: {stats['hit_rate']:.2f}")
    print(f"  Cache size: {stats['cache_size']}")
    
    # Test cache clearing
    converter.clear_cache()
    stats_after_clear = converter.get_cache_stats()
    
    if stats_after_clear['cache_size'] == 0:
        print("✓ Cache clearing successful")
    else:
        print("✗ Cache clearing failed")
        return False
    
    return True

def test_error_handling():
    """Test error handling and edge cases."""
    print("\n=== Testing Error Handling ===")
    
    converter = SupervisionConverter()
    
    # Test with None input
    result = converter.convert_to_supervision(None)
    if result is not None and len(result) == 0:
        print("✓ None input handled correctly")
    else:
        print("✗ None input not handled correctly")
        return False
    
    # Test with malformed data
    malformed_data = [
        {"invalid": "data"},
        {"bbox": [1, 2], "score": 0.5},  # Invalid bbox
        {"bbox": [1, 2, 3, 4], "score": "invalid"}  # Invalid score
    ]
    
    result = converter.convert_to_supervision(
        malformed_data,
        source_format='hailo',
        frame_shape=(100, 100)
    )
    
    # Should handle gracefully and return empty or partial results
    print(f"✓ Malformed data handled gracefully: {len(result) if result else 0} valid detections")
    
    return True

def main():
    """Run all tests."""
    print("Testing Enhanced Supervision Integration")
    print("=" * 50)
    
    tests = [
        ("Supervision Availability", test_supervision_availability),
        ("SupervisionConverter", test_supervision_converter),
        ("SupervisionTracker", test_supervision_tracker),
        ("EnhancedFrameProcessor", test_enhanced_frame_processor),
        ("Factory Functions", test_factory_functions),
        ("Performance and Caching", test_performance_and_caching),
        ("Error Handling", test_error_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    
    all_passed = True
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! The enhanced supervision integration is working correctly.")
        print("\nThe framework now supports:")
        print("  • Automatic Hailo to Supervision format conversion")
        print("  • Object tracking with ByteTrack")
        print("  • Intelligent caching for performance")
        print("  • Professional visualization with supervision annotators")
        print("  • Extensible design for new detection sources")
        print("  • Full backward compatibility")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
