#!/usr/bin/env python3
"""
Test script to verify the showcase framework exits properly after processing a video file.
"""

import subprocess
import time
import signal
import os

def test_showcase_exit():
    """Test that showcase framework exits after processing video."""
    print("Testing showcase framework exit behavior...")
    
    # Command to run showcase framework
    cmd = [
        "python", "showcase_framework.py",
        "--video-file", "videos/people1_30s.mp4",
        "--conf", "0.5",
        "--headless",
        "--realtime",
        "--fps", "15"
    ]
    
    # Set environment
    env = os.environ.copy()
    env['PATH'] = '/home/<USER>/jk/dev/hailotest/cam-env/bin:' + env['PATH']
    
    print("Starting showcase framework...")
    start_time = time.time()
    
    try:
        # Run the process with a timeout
        result = subprocess.run(
            cmd,
            cwd="/home/<USER>/jk/dev/hailotest",
            env=env,
            capture_output=True,
            text=True,
            timeout=120  # 2 minute timeout
        )
        
        elapsed = time.time() - start_time
        
        print(f"Process completed in {elapsed:.1f} seconds")
        print(f"Return code: {result.returncode}")
        
        # Check if it exited normally (not due to timeout)
        if result.returncode == 0:
            print("✅ Showcase framework exited normally!")
        elif result.returncode == 139:  # Segmentation fault
            print("⚠️  Process exited with segmentation fault (likely model cleanup issue)")
            print("   But the important part is that it DID exit (no hanging)")
        else:
            print(f"❌ Process exited with code {result.returncode}")
        
        # Check output for key indicators
        output = result.stdout + result.stderr
        
        if "No more frames from source" in output:
            print("✅ EOF detection working")
        
        if "Pipeline signaled stop and queues are empty" in output:
            print("✅ Pipeline shutdown working")
        
        if "Capture loop finished" in output:
            print("✅ Capture loop properly finished")
        
        # Show last few lines of output
        lines = output.strip().split('\n')
        print("\nLast few lines of output:")
        for line in lines[-5:]:
            print(f"  {line}")
            
    except subprocess.TimeoutExpired:
        print("❌ Process did not exit within timeout - still hanging!")
        return False
    
    return True

if __name__ == "__main__":
    success = test_showcase_exit()
    if success:
        print("\n🎉 Test passed! The showcase framework now exits properly.")
    else:
        print("\n💥 Test failed! The showcase framework is still hanging.")
