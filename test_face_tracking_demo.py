#!/usr/bin/env python3
"""
Test script for face_tracking_demo.py components
"""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required imports work."""
    print("Testing imports...")
    
    try:
        from face_tracking_demo import (
            FaceTrackingStats, TrackingTrailsVisualizer, 
            AdvancedFaceTrackingProcessor, create_video_source,
            create_face_detection_model, parse_arguments
        )
        print("✓ All imports successful")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_face_tracking_stats():
    """Test FaceTrackingStats class."""
    print("Testing FaceTrackingStats...")
    
    try:
        from face_tracking_demo import FaceTrackingStats
        
        stats = FaceTrackingStats()
        stats.update_frame(2, [1, 2])
        stats.update_frame(1, [1])
        stats.update_frame(0, [])
        
        summary = stats.get_summary()
        
        assert summary['total_frames'] == 3
        assert summary['frames_with_faces'] == 2
        assert summary['total_detections'] == 3
        assert summary['unique_faces_seen'] == 2
        
        print("✓ FaceTrackingStats test passed")
        return True
        
    except Exception as e:
        print(f"✗ FaceTrackingStats test failed: {e}")
        return False

def test_tracking_trails_visualizer():
    """Test TrackingTrailsVisualizer class."""
    print("Testing TrackingTrailsVisualizer...")
    
    try:
        from face_tracking_demo import TrackingTrailsVisualizer
        
        visualizer = TrackingTrailsVisualizer(max_trail_length=10)
        
        # Create a dummy frame
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Test drawing (should not crash)
        result_frame = visualizer.draw_trails(frame)
        
        assert result_frame.shape == frame.shape
        
        print("✓ TrackingTrailsVisualizer test passed")
        return True
        
    except Exception as e:
        print(f"✗ TrackingTrailsVisualizer test failed: {e}")
        return False

def test_argument_parsing():
    """Test command-line argument parsing."""
    print("Testing argument parsing...")
    
    try:
        from face_tracking_demo import parse_arguments
        import sys
        
        # Save original argv
        original_argv = sys.argv.copy()
        
        # Test with sample arguments
        sys.argv = [
            'face_tracking_demo.py',
            '--video-file', 'test.mp4',
            '--conf', '0.6',
            '--tracking-sensitivity', 'high',
            '--show-trails',
            '--headless'
        ]
        
        args = parse_arguments()
        
        assert args.video_file == 'test.mp4'
        assert args.conf == 0.6
        assert args.tracking_sensitivity == 'high'
        assert args.show_trails == True
        assert args.headless == True
        
        # Restore original argv
        sys.argv = original_argv
        
        print("✓ Argument parsing test passed")
        return True
        
    except Exception as e:
        print(f"✗ Argument parsing test failed: {e}")
        return False

def test_showcase_framework_integration():
    """Test integration with showcase_framework."""
    print("Testing showcase_framework integration...")
    
    try:
        from showcase_framework import (
            SUPERVISION_AVAILABLE, SupervisionConverter,
            EnhancedFrameProcessor, DummyModel
        )
        
        print(f"  Supervision available: {SUPERVISION_AVAILABLE}")
        
        # Test SupervisionConverter
        converter = SupervisionConverter()
        print("  ✓ SupervisionConverter created")
        
        # Test EnhancedFrameProcessor
        processor = EnhancedFrameProcessor(
            conf_threshold=0.5,
            enable_supervision=True,
            enable_tracking=False  # Disable tracking for simple test
        )
        print("  ✓ EnhancedFrameProcessor created")
        
        # Test DummyModel
        model = DummyModel()
        print("  ✓ DummyModel created")
        
        print("✓ Showcase framework integration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Showcase framework integration test failed: {e}")
        return False

def test_advanced_processor_creation():
    """Test AdvancedFaceTrackingProcessor creation."""
    print("Testing AdvancedFaceTrackingProcessor creation...")
    
    try:
        from face_tracking_demo import AdvancedFaceTrackingProcessor
        
        # Test with different configurations
        configs = [
            {'tracking_sensitivity': 'low', 'enable_tracking': False},
            {'tracking_sensitivity': 'medium', 'enable_tracking': True},
            {'tracking_sensitivity': 'high', 'show_trails': True}
        ]
        
        for i, config in enumerate(configs):
            processor = AdvancedFaceTrackingProcessor(
                conf_threshold=0.5,
                **config
            )
            print(f"  ✓ Configuration {i+1} successful")
        
        print("✓ AdvancedFaceTrackingProcessor creation test passed")
        return True
        
    except Exception as e:
        print(f"✗ AdvancedFaceTrackingProcessor creation test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("="*60)
    print("FACE TRACKING DEMO - COMPONENT TESTS")
    print("="*60)
    
    tests = [
        ("Imports", test_imports),
        ("FaceTrackingStats", test_face_tracking_stats),
        ("TrackingTrailsVisualizer", test_tracking_trails_visualizer),
        ("Argument Parsing", test_argument_parsing),
        ("Showcase Framework Integration", test_showcase_framework_integration),
        ("AdvancedFaceTrackingProcessor", test_advanced_processor_creation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
        print()
    
    print("="*60)
    print("TEST RESULTS:")
    print("="*60)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("="*60)
    
    if all_passed:
        print("🎉 All component tests passed!")
        print("The face tracking demo components are working correctly.")
        return 0
    else:
        print("❌ Some tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
